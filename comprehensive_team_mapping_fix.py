#!/usr/bin/env python3
"""
Comprehensive Team Mapping Fix

This script uses advanced pattern matching and fuzzy string matching to automatically
identify and fix team canonical mappings across the entire database at scale.
"""

import sys
import sqlite3
from difflib import SequenceMatcher
import re
from collections import defaultdict

sys.path.append('src')
from database.football_db import get_database

def normalize_team_name_for_matching(name):
    """Normalize team name for fuzzy matching."""
    # Convert to lowercase
    name = name.lower()
    
    # Remove common prefixes and suffixes
    name = re.sub(r'^(ca|cf|fc|ac|sc|afc|bfc|cfc|dfc|efc|ffc|gfc|hfc|ifc|jfc|kfc|lfc|mfc|nfc|ofc|pfc|qfc|rfc|sfc|tfc|ufc|vfc|wfc|xfc|yfc|zfc)\s+', '', name)
    name = re.sub(r'\s+(fc|f\.c\.|cf|c\.f\.|ac|a\.c\.|sc|s\.c\.)$', '', name)
    name = re.sub(r'\s+(united|utd|u\.s\.|city|c\.c\.|athletic|ath\.|sporting|wanderers|rovers)$', '', name)
    name = re.sub(r'\s+(juniors|jrs|jr|junior)$', '', name)
    name = re.sub(r'\s+(club|c\.|football|f\.|soccer|s\.)$', '', name)
    
    # Remove special characters and normalize spacing
    name = re.sub(r'[^\w\s]', '', name)
    name = re.sub(r'\s+', ' ', name.strip())
    
    return name

def calculate_similarity(name1, name2):
    """Calculate similarity between two team names."""
    # Direct comparison
    if name1.lower() == name2.lower():
        return 1.0
    
    # Normalized comparison
    norm1 = normalize_team_name_for_matching(name1)
    norm2 = normalize_team_name_for_matching(name2)
    
    if norm1 == norm2:
        return 0.95
    
    # Fuzzy matching
    similarity = SequenceMatcher(None, norm1, norm2).ratio()
    
    # Boost similarity for common patterns
    if (name1.lower().replace(' ', '') in name2.lower().replace(' ', '') or 
        name2.lower().replace(' ', '') in name1.lower().replace(' ', '')):
        similarity = max(similarity, 0.85)
    
    return similarity

def find_all_mapping_candidates():
    """Find all potential team mappings using comprehensive pattern matching."""
    mappings = []
    
    with get_database() as db:
        # Get all teams with their stats status
        query = """
            SELECT 
                t.team_id,
                t.team_name,
                t.league_id,
                l.league_name,
                t.canonical_team_id,
                CASE WHEN ts.team_id IS NOT NULL THEN 1 ELSE 0 END as has_stats
            FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            ORDER BY l.league_name, t.team_name
        """
        
        teams_df = db.execute_query(query)
        
        # Group teams by league
        leagues = teams_df.groupby('league_id')
        
        for league_id, league_teams in leagues:
            league_name = league_teams.iloc[0]['league_name']
            print(f"Processing league: {league_name}")
            
            # Get teams without stats and no canonical mapping
            teams_without_stats = league_teams[
                (league_teams['has_stats'] == 0) & 
                (league_teams['canonical_team_id'].isna())
            ]
            
            # Get teams with stats
            teams_with_stats = league_teams[league_teams['has_stats'] == 1]
            
            if teams_without_stats.empty or teams_with_stats.empty:
                continue
            
            # Find best matches for each team without stats
            for _, team_without in teams_without_stats.iterrows():
                best_match = None
                best_similarity = 0.0
                
                for _, team_with in teams_with_stats.iterrows():
                    # Skip if the team with stats already has a canonical mapping to another team
                    if not pd.isna(team_with['canonical_team_id']) and team_with['canonical_team_id'] != team_with['team_id']:
                        continue
                    
                    similarity = calculate_similarity(team_without['team_name'], team_with['team_name'])
                    
                    if similarity > best_similarity and similarity >= 0.75:  # 75% threshold
                        best_similarity = similarity
                        best_match = team_with
                
                if best_match is not None:
                    mappings.append({
                        'league_name': league_name,
                        'team_without_stats': team_without['team_name'],
                        'team_without_stats_id': team_without['team_id'],
                        'team_with_stats': best_match['team_name'],
                        'team_with_stats_id': best_match['team_id'],
                        'similarity': best_similarity
                    })
                    
                    print(f"  Found: '{team_without['team_name']}' -> '{best_match['team_name']}' ({best_similarity:.2f})")
    
    return mappings

def apply_mappings_in_batches(mappings, batch_size=100):
    """Apply mappings in batches for better performance."""
    total_mappings = len(mappings)
    print(f"\nApplying {total_mappings} mappings in batches of {batch_size}...")
    
    with get_database() as db:
        success_count = 0
        error_count = 0
        
        for i in range(0, total_mappings, batch_size):
            batch = mappings[i:i + batch_size]
            print(f"Processing batch {i//batch_size + 1}/{(total_mappings + batch_size - 1)//batch_size}")
            
            try:
                # Prepare batch update
                updates = [(mapping['team_with_stats_id'], mapping['team_without_stats_id']) 
                          for mapping in batch]
                
                # Execute batch update
                db.conn.executemany(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                    updates
                )
                
                success_count += len(batch)
                print(f"  ✅ Successfully updated {len(batch)} teams")
                
            except Exception as e:
                print(f"  ❌ Error in batch: {e}")
                error_count += len(batch)
        
        # Commit all changes
        db.conn.commit()
        
        print(f"\n📊 Final Results:")
        print(f"  ✅ Successfully applied: {success_count} mappings")
        print(f"  ❌ Errors: {error_count} mappings")
        print(f"  📈 Success rate: {success_count/total_mappings*100:.1f}%")

def verify_fix_effectiveness():
    """Verify how many teams now have access to statistics."""
    with get_database() as db:
        # Count teams without direct stats
        teams_without_direct_stats = db.execute_scalar("""
            SELECT COUNT(*) 
            FROM teams t 
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id 
            WHERE ts.team_id IS NULL
        """)
        
        # Count teams without any way to access stats (no direct stats and no canonical mapping)
        teams_without_any_stats_access = db.execute_scalar("""
            SELECT COUNT(*) 
            FROM teams t 
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id 
            WHERE ts.team_id IS NULL AND t.canonical_team_id IS NULL
        """)
        
        # Count teams with canonical mappings
        teams_with_canonical_mappings = db.execute_scalar("""
            SELECT COUNT(*) 
            FROM teams t 
            WHERE t.canonical_team_id IS NOT NULL AND t.canonical_team_id != t.team_id
        """)
        
        print(f"\n📈 Database Statistics After Fix:")
        print(f"  Teams without direct statistics: {teams_without_direct_stats:,}")
        print(f"  Teams with canonical mappings: {teams_with_canonical_mappings:,}")
        print(f"  Teams without ANY stats access: {teams_without_any_stats_access:,}")
        
        improvement = teams_without_direct_stats - teams_without_any_stats_access
        print(f"  Teams that gained stats access: {improvement:,}")

def main():
    """Main function."""
    print("🚀 Starting Comprehensive Team Mapping Fix...")
    print("This will identify and fix team canonical mappings across ALL leagues.")
    
    # Import pandas here to avoid import issues
    global pd
    import pandas as pd
    
    # Find all mapping candidates
    print("\n🔍 Phase 1: Finding mapping candidates...")
    mappings = find_all_mapping_candidates()
    
    if not mappings:
        print("✅ No additional mappings needed!")
        verify_fix_effectiveness()
        return
    
    print(f"\n📊 Found {len(mappings)} potential mappings")
    
    # Show sample mappings
    print("\n📋 Sample mappings (first 10):")
    for i, mapping in enumerate(mappings[:10]):
        print(f"  {i+1}. {mapping['league_name']}: '{mapping['team_without_stats']}' -> '{mapping['team_with_stats']}' ({mapping['similarity']:.2f})")
    
    if len(mappings) > 10:
        print(f"  ... and {len(mappings) - 10} more")
    
    # Ask for confirmation
    response = input(f"\nDo you want to apply all {len(mappings)} mappings? (y/N): ").strip().lower()
    
    if response == 'y':
        print("\n🔧 Phase 2: Applying mappings...")
        apply_mappings_in_batches(mappings)
        
        print("\n🔍 Phase 3: Verifying results...")
        verify_fix_effectiveness()
        
        print("\n🎉 Comprehensive team mapping fix complete!")
    else:
        print("❌ Mappings not applied.")

if __name__ == "__main__":
    main()
