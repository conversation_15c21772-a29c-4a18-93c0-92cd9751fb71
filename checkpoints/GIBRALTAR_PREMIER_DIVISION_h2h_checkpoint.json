{"league_name": "GIBRALTAR_PREMIER_DIVISION", "completed_urls": ["Europa Fc vs Glacis United Fc", "College 1975 Fc vs Manchester 62 Fc", "Glacis United Fc vs Mons Calpe Sc", "Lynx Fc vs Manchester 62 Fc", "Europa Point Fc vs Glacis United Fc", "College 1975 Fc vs Lions Gibraltar Fc", "Glacis United Fc vs Lynx Fc", "Lincoln Red Imps Fc vs Manchester 62 Fc", "Mons Calpe Sc vs St Josephs Fc", "Lions Gibraltar Fc vs St Josephs Fc", "Europa Fc vs Europa Point Fc", "College 1975 Fc vs Lynx Fc", "Europa Point Fc vs Fc Magpies", "Fc Magpies vs Lincoln Red Imps Fc", "Lynx Fc vs St Josephs Fc", "Manchester 62 Fc vs Mons Calpe Sc", "Glacis United Fc vs Manchester 62 Fc", "College 1975 Fc vs Lincoln Red Imps Fc", "College 1975 Fc vs Europa Fc", "College 1975 Fc vs Glacis United Fc", "Lincoln Red Imps Fc vs St Josephs Fc", "Glacis United Fc vs Lions Gibraltar Fc", "Manchester 62 Fc vs St Josephs Fc", "Lynx Fc vs Mons Calpe Sc", "Fc Magpies vs Manchester 62 Fc", "Lions Gibraltar Fc vs Manchester 62 Fc", "College 1975 Fc vs Fc Magpies", "Lions Gibraltar Fc vs Lynx Fc", "Europa Fc vs Fc Magpies", "Europa Point Fc vs Lions Gibraltar Fc", "Europa Point Fc vs Lynx Fc", "Europa Fc vs Manchester 62 Fc", "Europa Point Fc vs St Josephs Fc", "Fc Magpies vs Lynx Fc", "Glacis United Fc vs St Josephs Fc", "Fc Magpies vs Mons Calpe Sc", "Fc Magpies vs St Josephs Fc", "Lincoln Red Imps Fc vs Mons Calpe Sc", "Europa Fc vs Lions Gibraltar Fc", "Lincoln Red Imps Fc vs Lions Gibraltar Fc", "Europa Point Fc vs Lincoln Red Imps Fc", "Europa Fc vs Mons Calpe Sc", "Europa Fc vs Lynx Fc", "College 1975 Fc vs Mons Calpe Sc", "Lincoln Red Imps Fc vs Lynx Fc", "Europa Point Fc vs Mons Calpe Sc", "Glacis United Fc vs Lincoln Red Imps Fc", "College 1975 Fc vs St Josephs Fc", "Fc Magpies vs Glacis United Fc", "Fc Magpies vs Lions Gibraltar Fc", "Lions Gibraltar Fc vs Mons Calpe Sc", "Europa Fc vs St Josephs Fc", "College 1975 Fc vs Europa Point Fc", "Europa Fc vs Lincoln Red Imps Fc", "Europa Point Fc vs Manchester 62 Fc"], "failed_urls": [], "retry_data": {}, "permanently_failed_urls": [], "processed_since_last_retry": 55, "current_position": 55, "total_urls": 55, "last_updated": "2025-06-24T12:30:24.272965", "batch_size": 4, "max_retries": 3, "retry_cooldown_urls": 20}