#!/usr/bin/env python3
"""
Fix Premier League Team Mappings

This script specifically fixes team name mappings for the England Premier League
where teams like "Arsenal FC" should map to "Arsenal" which has the actual stats.
"""

import sys
import sqlite3

sys.path.append('src')
from database.football_db import get_database

def fix_premier_league_mappings():
    """Fix team mappings for England Premier League."""
    
    # Define the mappings based on the analysis
    mappings = [
        ("Arsenal FC", "Arsenal"),
        ("Aston Villa FC", "Aston Villa"),
        ("Brentford FC", "Brentford"),
        ("Chelsea FC", "Chelsea"),
        ("Crystal Palace FC", "Crystal Palace"),
        ("Everton FC", "Everton"),
        ("Leicester City FC", "Leicester City"),
        ("Manchester City FC", "Manchester City"),
        ("Southampton FC", "Southampton"),
        ("Tottenham Hotspur", "Tottenham"),
        ("Tottenham Hotspur FC", "Tottenham"),
        ("Manchester United", "Manchester Utd"),
        ("Manchester United FC", "Manchester Utd"),
        ("Newcastle United", "Newcastle Utd"),
        ("Newcastle United FC", "Newcastle Utd"),
        ("West Ham United", "West Ham Utd"),
        ("West Ham United FC", "West Ham Utd"),
        ("Nottingham Forest", "Nottm Forest"),
        ("Nottingham Forest FC", "Nottm Forest"),
        ("Wolverhampton Wanderers", "Wolverhampton"),
        ("Wolverhampton Wanderers FC", "Wolverhampton"),
        ("AFC Bournemouth", "Bournemouth"),
        ("Brighton & Hove Albion", "Brighton"),
        ("Ipswich Town FC", "Ipswich Town"),
    ]
    
    print(f"🔧 Fixing {len(mappings)} Premier League team mappings...")
    
    with get_database() as db:
        league_id_query = "SELECT league_id FROM leagues WHERE league_name = 'ENGLAND_PREMIER_LEAGUE'"
        league_id = db.execute_scalar(league_id_query)
        
        if not league_id:
            print("❌ England Premier League not found!")
            return
        
        applied_count = 0
        
        for team_without_stats, team_with_stats in mappings:
            try:
                # Get the team_id of the team with stats
                canonical_team_id = db.execute_scalar(
                    "SELECT team_id FROM teams WHERE team_name = ? AND league_id = ?",
                    (team_with_stats, league_id)
                )
                
                if not canonical_team_id:
                    print(f"⚠️  Warning: '{team_with_stats}' not found, skipping '{team_without_stats}'")
                    continue
                
                # Update the team without stats to point to the canonical team
                rows_affected = db.conn.execute(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_name = ? AND league_id = ?",
                    (canonical_team_id, team_without_stats, league_id)
                ).rowcount
                
                if rows_affected > 0:
                    print(f"✅ '{team_without_stats}' -> '{team_with_stats}' (team_id: {canonical_team_id})")
                    applied_count += 1
                else:
                    print(f"⚠️  '{team_without_stats}' not found in database")
                    
            except Exception as e:
                print(f"❌ Error mapping '{team_without_stats}': {e}")
        
        db.conn.commit()
        print(f"\n✅ Successfully applied {applied_count} mappings!")

def verify_mappings():
    """Verify that the mappings were applied correctly."""
    print("\n🔍 Verifying mappings...")
    
    with get_database() as db:
        # Check teams without stats in Premier League
        query = """
            SELECT t.team_name, t.canonical_team_id,
                   CASE WHEN ts.team_id IS NOT NULL THEN 'HAS_STATS' ELSE 'NO_STATS' END as status
            FROM teams t 
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id 
            JOIN leagues l ON t.league_id = l.league_id 
            WHERE l.league_name = 'ENGLAND_PREMIER_LEAGUE' 
            AND ts.team_id IS NULL
            ORDER BY t.team_name
        """
        
        teams_without_stats = db.execute_query(query)
        
        if teams_without_stats.empty:
            print("✅ All Premier League teams now have statistics access!")
        else:
            print(f"⚠️  {len(teams_without_stats)} teams still without direct stats:")
            for _, row in teams_without_stats.iterrows():
                canonical_status = "mapped" if row['canonical_team_id'] else "unmapped"
                print(f"  - {row['team_name']} ({canonical_status})")

def main():
    """Main function."""
    print("🏴󠁧󠁢󠁥󠁮󠁧󠁿 Fixing England Premier League team mappings...")
    
    fix_premier_league_mappings()
    verify_mappings()
    
    print("\n🎉 Premier League team mapping fix complete!")

if __name__ == "__main__":
    main()
