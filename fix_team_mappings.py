#!/usr/bin/env python3
"""
Fix Team Name Mappings

This script identifies teams with similar names but missing canonical mappings
and automatically creates the appropriate canonical_team_id mappings.

This fixes issues where teams like "Liverpool FC" don't show statistics
because they should map to "Liverpool" which has the actual stats.
"""

import sys
import sqlite3
from difflib import SequenceMatcher
import re

sys.path.append('src')
from database.football_db import get_database

def similarity(a, b):
    """Calculate similarity between two strings."""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def normalize_name_for_comparison(name):
    """Normalize team name for comparison by removing common suffixes and standardizing."""
    # Remove common suffixes
    name = re.sub(r'\s+(FC|F\.C\.|CF|C\.F\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(United|Utd|U\.S\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(City|C\.C\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(Athletic|Ath\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(Sporting|S\.C\.)$', '', name, flags=re.IGNORECASE)
    
    # Standardize spacing and case
    name = re.sub(r'\s+', ' ', name.strip())
    return name.lower()

def find_canonical_mappings_by_pattern():
    """Find teams that should have canonical mappings using common patterns."""
    mappings = []

    with get_database() as db:
        # Focus on common patterns that are likely to be correct mappings
        pattern_query = """
            SELECT
                t1.team_id as team_without_stats_id,
                t1.team_name as team_without_stats,
                t2.team_id as team_with_stats_id,
                t2.team_name as team_with_stats,
                l.league_name
            FROM teams t1
            JOIN teams t2 ON t1.league_id = t2.league_id AND t1.team_id != t2.team_id
            JOIN leagues l ON t1.league_id = l.league_id
            LEFT JOIN team_stats ts1 ON t1.team_id = ts1.team_id
            LEFT JOIN team_stats ts2 ON t2.team_id = ts2.team_id
            WHERE ts1.team_id IS NULL  -- t1 has no stats
            AND ts2.team_id IS NOT NULL  -- t2 has stats
            AND t1.canonical_team_id IS NULL  -- t1 not already mapped
            AND (
                -- Pattern 1: Team with "FC" suffix maps to team without
                (t1.team_name = t2.team_name || ' FC' OR t1.team_name = t2.team_name || ' F.C.')
                OR
                -- Pattern 2: Team with "United" maps to team with "Utd"
                (t1.team_name LIKE '%United' AND t2.team_name = REPLACE(t1.team_name, 'United', 'Utd'))
                OR
                -- Pattern 3: Full name maps to shortened name
                (t1.team_name LIKE t2.team_name || ' %' AND LENGTH(t1.team_name) - LENGTH(t2.team_name) < 20)
                OR
                -- Pattern 4: Case variations
                (LOWER(t1.team_name) = LOWER(t2.team_name) AND t1.team_name != t2.team_name)
            )
            ORDER BY l.league_name, t1.team_name
        """

        results = db.execute_query(pattern_query)

        for _, row in results.iterrows():
            mappings.append({
                'league_name': row['league_name'],
                'team_without_stats': row['team_without_stats'],
                'team_without_stats_id': row['team_without_stats_id'],
                'team_with_stats': row['team_with_stats'],
                'team_with_stats_id': row['team_with_stats_id'],
                'similarity': 1.0  # These are pattern-based, so high confidence
            })

            print(f"  Found mapping: '{row['team_without_stats']}' -> '{row['team_with_stats']}' in {row['league_name']}")

    return mappings

def apply_mappings(mappings, dry_run=True):
    """Apply the canonical team mappings to the database."""
    if dry_run:
        print(f"\n=== DRY RUN: Would apply {len(mappings)} mappings ===")
        for mapping in mappings:
            print(f"League: {mapping['league_name']}")
            print(f"  SET canonical_team_id = {mapping['team_with_stats_id']} for '{mapping['team_without_stats']}'")
            print(f"  (maps to '{mapping['team_with_stats']}' with similarity {mapping['similarity']:.2f})")
            print()
        return
    
    print(f"\n=== Applying {len(mappings)} mappings ===")
    
    with get_database() as db:
        for mapping in mappings:
            try:
                db.conn.execute(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                    (mapping['team_with_stats_id'], mapping['team_without_stats_id'])
                )
                print(f"✅ Updated '{mapping['team_without_stats']}' -> '{mapping['team_with_stats']}'")
            except Exception as e:
                print(f"❌ Error updating '{mapping['team_without_stats']}': {e}")
        
        db.conn.commit()
        print(f"\n✅ Successfully applied {len(mappings)} mappings!")

def main():
    """Main function."""
    print("🔍 Finding teams that need canonical mappings using pattern matching...")

    mappings = find_canonical_mappings_by_pattern()

    if not mappings:
        print("✅ No additional mappings needed!")
        return

    print(f"\n📊 Found {len(mappings)} potential mappings")

    # Show dry run first
    apply_mappings(mappings, dry_run=True)

    # Ask for confirmation
    response = input("\nDo you want to apply these mappings? (y/N): ").strip().lower()

    if response == 'y':
        apply_mappings(mappings, dry_run=False)
    else:
        print("❌ Mappings not applied.")

if __name__ == "__main__":
    main()
