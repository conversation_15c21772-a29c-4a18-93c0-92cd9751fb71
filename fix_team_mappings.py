#!/usr/bin/env python3
"""
Fix Team Name Mappings

This script identifies teams with similar names but missing canonical mappings
and automatically creates the appropriate canonical_team_id mappings.

This fixes issues where teams like "Liverpool FC" don't show statistics
because they should map to "Liverpool" which has the actual stats.
"""

import sys
import sqlite3
from difflib import SequenceMatcher
import re

sys.path.append('src')
from database.football_db import get_database

def similarity(a, b):
    """Calculate similarity between two strings."""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def normalize_name_for_comparison(name):
    """Normalize team name for comparison by removing common suffixes and standardizing."""
    # Remove common suffixes
    name = re.sub(r'\s+(FC|F\.C\.|CF|C\.F\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(United|Utd|U\.S\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(City|C\.C\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(Athletic|Ath\.)$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\s+(Sporting|S\.C\.)$', '', name, flags=re.IGNORECASE)
    
    # Standardize spacing and case
    name = re.sub(r'\s+', ' ', name.strip())
    return name.lower()

def find_canonical_mappings():
    """Find teams that should have canonical mappings."""
    mappings = []
    
    with get_database() as db:
        # Get all teams grouped by league
        teams_query = """
            SELECT t.team_id, t.team_name, t.league_id, l.league_name, t.canonical_team_id,
                   CASE WHEN ts.team_id IS NOT NULL THEN 1 ELSE 0 END as has_stats
            FROM teams t
            JOIN leagues l ON t.league_id = l.league_id
            LEFT JOIN team_stats ts ON t.team_id = ts.team_id
            ORDER BY l.league_name, t.team_name
        """
        
        teams_df = db.execute_query(teams_query)
        
        # Group by league
        for league_id in teams_df['league_id'].unique():
            league_teams = teams_df[teams_df['league_id'] == league_id]
            league_name = league_teams.iloc[0]['league_name']
            
            print(f"\nProcessing league: {league_name}")
            
            # Find teams without stats that might map to teams with stats
            teams_without_stats = league_teams[
                (league_teams['has_stats'] == 0) & 
                (league_teams['canonical_team_id'].isna())
            ]
            
            teams_with_stats = league_teams[league_teams['has_stats'] == 1]
            
            for _, team_without in teams_without_stats.iterrows():
                best_match = None
                best_similarity = 0.0
                
                # Compare with teams that have stats
                for _, team_with in teams_with_stats.iterrows():
                    # Skip if already mapped
                    if team_with['canonical_team_id'] is not None:
                        continue
                        
                    # Calculate similarity
                    sim1 = similarity(team_without['team_name'], team_with['team_name'])
                    
                    # Also compare normalized names
                    norm1 = normalize_name_for_comparison(team_without['team_name'])
                    norm2 = normalize_name_for_comparison(team_with['team_name'])
                    sim2 = similarity(norm1, norm2)
                    
                    # Use the higher similarity
                    sim = max(sim1, sim2)
                    
                    # If normalized names are identical, that's a perfect match
                    if norm1 == norm2:
                        sim = 1.0
                    
                    if sim > best_similarity and sim >= 0.7:  # 70% similarity threshold
                        best_similarity = sim
                        best_match = team_with
                
                if best_match is not None:
                    mappings.append({
                        'league_name': league_name,
                        'team_without_stats': team_without['team_name'],
                        'team_without_stats_id': team_without['team_id'],
                        'team_with_stats': best_match['team_name'],
                        'team_with_stats_id': best_match['team_id'],
                        'similarity': best_similarity
                    })
                    
                    print(f"  Found mapping: '{team_without['team_name']}' -> '{best_match['team_name']}' (similarity: {best_similarity:.2f})")
    
    return mappings

def apply_mappings(mappings, dry_run=True):
    """Apply the canonical team mappings to the database."""
    if dry_run:
        print(f"\n=== DRY RUN: Would apply {len(mappings)} mappings ===")
        for mapping in mappings:
            print(f"League: {mapping['league_name']}")
            print(f"  SET canonical_team_id = {mapping['team_with_stats_id']} for '{mapping['team_without_stats']}'")
            print(f"  (maps to '{mapping['team_with_stats']}' with similarity {mapping['similarity']:.2f})")
            print()
        return
    
    print(f"\n=== Applying {len(mappings)} mappings ===")
    
    with get_database() as db:
        for mapping in mappings:
            try:
                db.conn.execute(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                    (mapping['team_with_stats_id'], mapping['team_without_stats_id'])
                )
                print(f"✅ Updated '{mapping['team_without_stats']}' -> '{mapping['team_with_stats']}'")
            except Exception as e:
                print(f"❌ Error updating '{mapping['team_without_stats']}': {e}")
        
        db.conn.commit()
        print(f"\n✅ Successfully applied {len(mappings)} mappings!")

def main():
    """Main function."""
    print("🔍 Finding teams that need canonical mappings...")
    
    mappings = find_canonical_mappings()
    
    if not mappings:
        print("✅ No additional mappings needed!")
        return
    
    print(f"\n📊 Found {len(mappings)} potential mappings")
    
    # Show dry run first
    apply_mappings(mappings, dry_run=True)
    
    # Ask for confirmation
    response = input("\nDo you want to apply these mappings? (y/N): ").strip().lower()
    
    if response == 'y':
        apply_mappings(mappings, dry_run=False)
    else:
        print("❌ Mappings not applied.")

if __name__ == "__main__":
    main()
